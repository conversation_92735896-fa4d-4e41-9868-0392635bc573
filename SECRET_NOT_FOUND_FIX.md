# Secret Not Found Error Fix

## Problem Description

The `donation-receipt-backend` deployment was failing with the error:
```
Error: secret "donation-receipt-backend-secrets" not found
```

## Root Cause Analysis

### Issue Identified
The `secret.yaml` file existed in the deployment directory but was **not being applied** to the cluster because it was missing from the `kustomization.yaml` resources list.

### Evidence
1. **Secret file exists**: `deployments/donation-receipt-backend/overlays/dev/secret.yaml` ✅
2. **Deployment references secret**: `deployment.yaml` contains 38 references to `donation-receipt-backend-secrets` ✅
3. **Kustomization missing secret**: `kustomization.yaml` did not include `secret.yaml` in resources ❌

### Files Affected
```
deployments/donation-receipt-backend/overlays/dev/kustomization.yaml
deployments/donation-receipt-backend/overlays/staging/kustomization.yaml  
deployments/donation-receipt-backend/overlays/production/kustomization.yaml
```

## Fix Applied

### 1. Updated Existing Deployments
Added `secret.yaml` to the resources list in all donation-receipt-backend kustomization files:

**Before:**
```yaml
resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- ingress.yaml
# Note: secret.yaml is conditionally included based on APPLICATION_TYPE
```

**After:**
```yaml
resources:
- deployment.yaml
- service.yaml
- configmap.yaml
- secret.yaml
- ingress.yaml
# Note: secret.yaml is included for backend applications
```

### 2. Updated Base Templates
Fixed the same issue in base template files to prevent future occurrences:
- `manifests/overlays/dev/kustomization.yaml`
- `manifests/overlays/staging/kustomization.yaml`
- `manifests/overlays/production/kustomization.yaml`

## Why This Happened

The comment in kustomization.yaml files stated:
```yaml
# Note: secret.yaml is conditionally included based on APPLICATION_TYPE
# For react-frontend applications, secrets are handled differently
```

However, there was **no actual logic** to conditionally include the secret.yaml file. The comment was misleading - the file should have been included for all backend applications (springboot-backend, django-backend, nest-backend).

## Verification

After applying this fix:
1. ✅ The `secret.yaml` file will be applied to the cluster
2. ✅ The deployment will find the required secret `donation-receipt-backend-secrets`
3. ✅ All environment variables referencing the secret will resolve correctly
4. ✅ Future deployments using the templates will include secrets automatically

## Impact

- **Immediate**: Fixes the current deployment error for donation-receipt-backend
- **Future**: Prevents the same issue from occurring in new deployments
- **Consistency**: Ensures all backend applications include their secrets properly

## Related Files

### Fixed Files
- `deployments/donation-receipt-backend/overlays/*/kustomization.yaml` (3 files)
- `manifests/overlays/*/kustomization.yaml` (3 files)

### Dependent Files (No changes needed)
- `deployments/donation-receipt-backend/overlays/*/secret.yaml` (Already correct)
- `deployments/donation-receipt-backend/overlays/*/deployment.yaml` (Already correct)

The fix is minimal and surgical - only adding the missing resource reference without changing any other functionality.
