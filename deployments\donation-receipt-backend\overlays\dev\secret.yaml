apiVersion: v1
kind: Secret
metadata:
  name: donation-receipt-backend-secrets
  labels:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/component: secrets
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/version: "673d3c80"
    app.kubernetes.io/managed-by: argocd
    environment: dev
type: Opaque
data:
  # Development Environment Secrets
  # These placeholders will be replaced with base64-encoded values from secrets_encoded

  
  # Spring Boot specific secrets
  SPRING_DATASOURCE_URL: ********************************************************************************************************************************************************************
  EMAIL_DONATIONS: ****************************************
  EMAIL_HOST: c210cC5nbWFpbC5jb20=
  EMAIL_PORT: NTg3
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: dHJ1ZQ==
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: dHJ1ZQ==
  

  

  

  
  # Essential Authentication Secrets
  JWT_SECRET: dmVyeWxvbmdjaGlkaGdhbmlzZWNyZXR5b3V3aWxsbm90dW5kZXJzdGFuZGRzYWRzYWtkanNhZGpzYWRuY3p4Y3psa2pzYWtuZGxzYWtuZHNhamNuY2tuc2FkanNhbGtkbnNhbmRrc2FramNzbmFrbnNhZGtqc2FkbnNhZG5zYWRsa2pza25zYWRsbnNha2Ruc2FuZHNqZGtzYWpka3NhZGtzamRrc2pka2pzYWRuc2FkanNhZG5zYWtzYWpkbGtzYW5saw==
  

  # Database Credentials (Development)
  DB_USER: c3ByaW5nX2Rldl91c2Vy
  DB_PASSWORD: QVZOU19ESF9GWmNvZEFUVHZpc2Z0dE9S
  DB_HOST: ZGItcG9zdGdyZXNxbC1ibHIxLTQ1MTY3LWRvLXVzZXItMjQzMDA1MTAtMC5pLmRiLm9uZGlnaXRhbG9jZWFuLmNvbQ==
  DB_PORT: MjUwNjA=
  DB_NAME: c3ByaW5nX2Rldl9kYg==
  DB_SSL_MODE: cmVxdWlyZQ==

  # SMTP Configuration (Development)
  SMTP_USER: ****************************************
  SMTP_PASS: ZnFhY3RlaGFmbXpsbHR6eg==

  # OAuth2 Configuration (Development)
  GOOGLE_CLIENT_ID: MTA3Mzk4MTg2NDUzOC0zdWlpazcyb2hzZnIyb3Vpb3JvcjNmbTFqcWM0OTNvcy5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbQ==
  GOOGLE_CLIENT_SECRET: R09DU1BYLTcyRjBONEg5aGlMSVk1U3o1Z3pCczI5OEFBYlQ=
  

  

  

  # Development-specific secrets
  # DEBUG_MODE: DYNAMIC_DEBUG_MODE_B64
  # LOG_LEVEL: DYNAMIC_LOG_LEVEL_B64
