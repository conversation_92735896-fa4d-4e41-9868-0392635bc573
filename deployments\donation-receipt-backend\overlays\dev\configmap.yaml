apiVersion: v1
kind: ConfigMap
metadata:
  name: donation-receipt-backend-config
  labels:
    app: donation-receipt-backend
    app.kubernetes.io/name: donation-receipt-backend
    app.kubernetes.io/component: config
    app.kubernetes.io/part-of: donation-receipt-backend
    app.kubernetes.io/version: "673d3c80"
    app.kubernetes.io/managed-by: argocd
data:
  # Application Configuration
  APP_NAME: "donation-receipt-backend"
  PROJECT_ID: "donation-receipt-backend"
  APPLICATION_TYPE: "springboot-backend"
  SOURCE_REPO: "ChidhagniConsulting/Donation-Receipt-Backend"
  SOURCE_BRANCH: "78/merge"
  COMMIT_SHA: "673d3c80"

  # Database Configuration (DigitalOcean PostgreSQL)
  # Database connection details are configured via environment variables in deployment from secrets
  
  # Application URLs
  APP_URL: "http://localhost:8080"
  API_URL: "http://localhost:8080/api"
  
  # Common Backend Configuration
  SERVER_PORT: "8080"

  NODE_ENV: "dev"
  PORT: "8080"

  
  # Spring Boot Configuration
  SPRING_PROFILES_ACTIVE: "dev"
  SPRING_APPLICATION_NAME: "donation-receipt-backend"

  # SPRING_DATASOURCE_USERNAME is configured via environment variables in deployment
  SPRING_JPA_HIBERNATE_DDL_AUTO: "none"
  SPRING_JPA_SHOW_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_DIALECT: "org.hibernate.dialect.PostgreSQLDialect"
  SPRING_JPA_PROPERTIES_HIBERNATE_FORMAT_SQL: "true"
  SPRING_JPA_PROPERTIES_HIBERNATE_JDBC_TIME_ZONE: "UTC"

  # Spring Boot Management & Actuator
  MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: "health,info,metrics,prometheus"
  MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: "always"
  MANAGEMENT_HEALTH_DEFAULTS_ENABLED: "true"
  MANAGEMENT_HEALTH_PROBES_ENABLED: "true"
  MANAGEMENT_HEALTH_MAIL_ENABLED: "false"
  MANAGEMENT_ENDPOINT_HEALTH_PROBES_ADD_ADDITIONAL_PATHS: "true"
  MANAGEMENT_HEALTH_LIVENESSSTATE_ENABLED: "true"
  MANAGEMENT_HEALTH_READINESSSTATE_ENABLED: "true"

  # JVM Configuration - Optimized for development (1Gi container limit)
  JAVA_OPTS: "-Xms256m -Xmx768m -XX:+UseG1GC -XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0"

  EMAIL_DONATIONS: "<EMAIL>"
  EMAIL_HOST: "smtp.gmail.com"
  EMAIL_PORT: "587"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_AUTH: "true"
  SPRING_MAIL_PROPERTIES_MAIL_SMTP_STARTTLS_ENABLE: "true"

  # Logging Configuration Override - Fix for Logback configuration error
  # Disable the problematic logback-dev.xml configuration file
  LOGGING_CONFIG: ""
  # Use Spring Boot's default logging configuration with environment variables
  LOGGING_LEVEL_ROOT: "INFO"
  LOGGING_LEVEL_COM_CHIDHAGNI: "DEBUG"
  LOGGING_PATTERN_CONSOLE: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
  LOGGING_PATTERN_FILE: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  # Use simple file appender without problematic rolling policy
  LOGGING_FILE_NAME: "/tmp/donation-receipt-service.log"
